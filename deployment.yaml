apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: mpesa-channels-oat
  name: one-platform-um
  labels:
    app: one-platform-um
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-um
  template:
    metadata:
      labels:
        app: one-platform-um
    spec:
      imagePullSecrets:
        - name: harbor-registry-key
      containers:
      - name: one-platform-um
        image: et02-harbor.safaricomet.net/mpesa_channels/one-platform-um-backend-oat:195
        imagePullPolicy: IfNotPresent
        envFrom:
          - secretRef:
              name: common-default-secret
          - configMapRef:
              name: common-default-cm
       
        ports:
        - containerPort: 2016
        volumeMounts:
        - mountPath: /app/upload
          name: one-platform-um-volume
      volumes:
      - name: one-platform-um-volume
        persistentVolumeClaim:
          claimName: one-platform-um-doc-oat-claim
